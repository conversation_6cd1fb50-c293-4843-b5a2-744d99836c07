<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\KostImage;
use App\Services\FileUploadService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class FileUploadController extends Controller
{
    private FileUploadService $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->middleware('auth:sanctum');
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Upload single image
     */
    public function uploadImage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|image|mimes:jpeg,png,jpg,webp|max:2048',
            'alt_text' => 'nullable|string|max:255',
        ], [
            'image.required' => 'Gambar harus dipilih.',
            'image.image' => 'File harus berupa gambar.',
            'image.mimes' => 'Format gambar harus JPEG, PNG, JPG, atau WEBP.',
            'image.max' => 'Ukuran gambar maksimal 2MB.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $result = $this->fileUploadService->uploadKostImage(
                $request->file('image'),
                'temp_' . Auth::id()
            );

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['error'],
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => 'Gambar berhasil diupload.',
                'data' => [
                    'path' => $result['path'],
                    'url' => $result['url'],
                    'thumbnail_url' => $result['thumbnail_url'],
                    'filename' => $result['filename'],
                    'size' => $result['size'],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengupload gambar.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Upload multiple images
     */
    public function uploadMultipleImages(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'images' => 'required|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:2048',
        ], [
            'images.required' => 'Minimal satu gambar harus dipilih.',
            'images.array' => 'Format data gambar tidak valid.',
            'images.max' => 'Maksimal 10 gambar dapat diupload sekaligus.',
            'images.*.image' => 'Semua file harus berupa gambar.',
            'images.*.mimes' => 'Format gambar harus JPEG, PNG, JPG, atau WEBP.',
            'images.*.max' => 'Ukuran setiap gambar maksimal 2MB.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $result = $this->fileUploadService->uploadMultipleKostImages(
                $request->file('images'),
                'temp_' . Auth::id()
            );

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success']
                    ? "Berhasil mengupload {$result['uploaded_count']} dari {$result['total_files']} gambar."
                    : 'Gagal mengupload gambar.',
                'data' => [
                    'uploaded_count' => $result['uploaded_count'],
                    'total_files' => $result['total_files'],
                    'results' => $result['results'],
                    'errors' => $result['errors'],
                ],
            ], $result['success'] ? 200 : 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengupload gambar.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Delete uploaded image
     */
    public function deleteImage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
        ], [
            'path.required' => 'Path gambar harus disediakan.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Cek apakah gambar sedang digunakan di database
            $isUsed = KostImage::where('image_path', $request->path)->exists();

            if ($isUsed) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gambar sedang digunakan dan tidak dapat dihapus.',
                ], 400);
            }

            $deleted = $this->fileUploadService->deleteKostImage($request->path);

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Gambar berhasil dihapus.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gagal menghapus gambar.',
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus gambar.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Get image information
     */
    public function getImageInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
        ], [
            'path.required' => 'Path gambar harus disediakan.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $info = $this->fileUploadService->getImageInfo($request->path);

            if (!$info['exists']) {
                return response()->json([
                    'success' => false,
                    'message' => $info['error'] ?? 'Gambar tidak ditemukan.',
                ], 404);
            }

            if (isset($info['error'])) {
                return response()->json([
                    'success' => false,
                    'message' => $info['error'],
                ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $info,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil informasi gambar.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Cleanup unused images (admin only)
     */
    public function cleanupUnusedImages(): JsonResponse
    {
        $user = Auth::user();

        if (!$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak. Hanya admin yang dapat menjalankan cleanup.',
            ], 403);
        }

        try {
            $result = $this->fileUploadService->cleanupUnusedImages();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success']
                    ? "Berhasil menghapus {$result['deleted_count']} gambar yang tidak terpakai."
                    : 'Gagal melakukan cleanup.',
                'data' => [
                    'deleted_count' => $result['deleted_count'] ?? 0,
                    'errors' => $result['errors'] ?? [],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat melakukan cleanup.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
