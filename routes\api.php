<?php

use App\Http\Controllers\Api\AiSearchController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// AI Search Routes
Route::middleware(['auth:sanctum'])->prefix('ai-search')->group(function () {
    Route::post('/search', [AiSearchController::class, 'search']);
    Route::get('/history', [AiSearchController::class, 'history']);
    Route::get('/stats', [AiSearchController::class, 'stats']);
});
