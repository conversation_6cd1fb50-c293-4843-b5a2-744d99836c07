<?php

namespace App\Http\Controllers;

use App\Models\Inquiry;
use App\Models\Kost;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class InquiryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Membuat inquiry baru
     */
    public function store(Request $request, Kost $kost)
    {
        // Pastikan user adalah pencari kost
        if (!Auth::user()->isPencariKost()) {
            return redirect()->back()->with('error', '<PERSON>ya pencari kost yang dapat mengirim inquiry.');
        }

        // Pastikan kost disetujui dan aktif
        if ($kost->status !== 'approved' || !$kost->is_active) {
            return redirect()->back()->with('error', 'Kost tidak tersedia untuk inquiry.');
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|min:10|max:1000',
            'contact_preference' => 'required|in:email,phone,whatsapp',
        ], [
            'message.required' => 'Pesan harus diisi.',
            'message.min' => 'Pesan minimal 10 karakter.',
            'message.max' => 'Pesan maksimal 1000 karakter.',
            'contact_preference.required' => 'Pilih cara kontak yang diinginkan.',
            'contact_preference.in' => 'Cara kontak tidak valid.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Cek apakah user sudah pernah mengirim inquiry untuk kost ini dalam 24 jam terakhir
        $existingInquiry = Inquiry::where('kost_id', $kost->id)
            ->where('user_id', Auth::id())
            ->where('created_at', '>=', now()->subDay())
            ->first();

        if ($existingInquiry) {
            return redirect()->back()->with('error', 'Anda sudah mengirim inquiry untuk kost ini dalam 24 jam terakhir.');
        }

        // Buat inquiry baru
        $inquiry = Inquiry::create([
            'kost_id' => $kost->id,
            'user_id' => Auth::id(),
            'message' => $request->message,
            'contact_preference' => $request->contact_preference,
        ]);

        // Buat notifikasi untuk pemilik kost
        Notification::createInquiryNotification(
            $kost->owner_id,
            $kost->name,
            $inquiry->id
        );

        return redirect()->back()->with('success', 'Inquiry berhasil dikirim. Pemilik kost akan menghubungi Anda segera.');
    }

    /**
     * Menampilkan detail inquiry
     */
    public function show(Inquiry $inquiry)
    {
        // Pastikan user adalah pemilik inquiry atau pemilik kost
        $user = Auth::user();
        if ($inquiry->user_id !== $user->id && $inquiry->kost->owner_id !== $user->id) {
            abort(403);
        }

        $inquiry->load(['kost.images', 'user']);

        return inertia('Inquiry/Show', [
            'inquiry' => $inquiry,
        ]);
    }

    /**
     * Update status inquiry (untuk pemilik kost)
     */
    public function updateStatus(Request $request, Inquiry $inquiry)
    {
        $user = Auth::user();

        // Pastikan user adalah pemilik kost
        if ($inquiry->kost->owner_id !== $user->id) {
            abort(403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:responded,closed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator);
        }

        if ($request->status === 'responded') {
            $inquiry->markAsResponded();
            $message = 'Inquiry ditandai sebagai sudah direspon.';
        } else {
            $inquiry->close();
            $message = 'Inquiry ditutup.';
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Hapus inquiry (hanya untuk pemilik inquiry)
     */
    public function destroy(Inquiry $inquiry)
    {
        $user = Auth::user();

        // Pastikan user adalah pemilik inquiry
        if ($inquiry->user_id !== $user->id) {
            abort(403);
        }

        $inquiry->delete();

        return redirect()->back()->with('success', 'Inquiry berhasil dihapus.');
    }
}
