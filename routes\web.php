<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        $user = Auth::user();

        // Redirect berdasarkan role
        return match ($user->role) {
            'pencari_kost' => redirect()->route('pencari.dashboard'),
            'pemilik_kost' => redirect()->route('pemilik.dashboard'),
            'admin' => redirect()->route('admin.dashboard'),
            default => Inertia::render('dashboard'),
        };
    })->name('dashboard');
});

// Routes untuk Pencari Kost
Route::middleware(['auth', 'verified', 'role:pencari_kost'])->prefix('pencari')->name('pencari.')->group(function () {
    Route::get('dashboard', [App\Http\Controllers\PencariKostController::class, 'dashboard'])->name('dashboard');
    Route::get('search', [App\Http\Controllers\PencariKostController::class, 'search'])->name('search');
    Route::get('kost/{kost}', [App\Http\Controllers\PencariKostController::class, 'show'])->name('kost.show');
    Route::get('inquiries', [App\Http\Controllers\PencariKostController::class, 'inquiries'])->name('inquiries');
    Route::get('notifications', [App\Http\Controllers\PencariKostController::class, 'notifications'])->name('notifications');
});

// Routes untuk Inquiry
Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('kost/{kost}/inquiry', [App\Http\Controllers\InquiryController::class, 'store'])->name('inquiries.store');
    Route::get('inquiry/{inquiry}', [App\Http\Controllers\InquiryController::class, 'show'])->name('inquiries.show');
    Route::patch('inquiry/{inquiry}/status', [App\Http\Controllers\InquiryController::class, 'updateStatus'])->name('inquiries.update-status');
    Route::delete('inquiry/{inquiry}', [App\Http\Controllers\InquiryController::class, 'destroy'])->name('inquiries.destroy');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
