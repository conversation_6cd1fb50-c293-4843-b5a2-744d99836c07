<?php

namespace App\Http\Controllers;

use App\Models\Kost;
use App\Models\KostFacility;
use App\Models\KostImage;
use App\Models\Inquiry;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class PemilikKostController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:pemilik_kost']);
    }

    /**
     * Dashboard utama pemilik kost
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Statistik untuk dashboard
        $stats = [
            'total_kosts' => $user->kosts()->count(),
            'approved_kosts' => $user->kosts()->approved()->count(),
            'pending_kosts' => $user->kosts()->where('status', 'pending')->count(),
            'total_inquiries' => Inquiry::whereHas('kost', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            })->count(),
            'pending_inquiries' => Inquiry::whereHas('kost', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            })->pending()->count(),
            'unread_notifications' => $user->notifications()->unread()->count(),
        ];

        // Kost milik user
        $myKosts = $user->kosts()
            ->with(['facilities', 'images'])
            ->latest()
            ->limit(6)
            ->get();

        // Inquiry terbaru untuk kost milik user
        $recentInquiries = Inquiry::with(['kost', 'user'])
            ->whereHas('kost', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            })
            ->latest()
            ->limit(5)
            ->get();

        // Notifikasi terbaru
        $recentNotifications = $user->notifications()
            ->latest()
            ->limit(5)
            ->get();

        return Inertia::render('PemilikKost/Dashboard', [
            'stats' => $stats,
            'myKosts' => $myKosts,
            'recentInquiries' => $recentInquiries,
            'recentNotifications' => $recentNotifications,
        ]);
    }

    /**
     * Daftar kost milik user
     */
    public function index()
    {
        $user = Auth::user();

        $kosts = $user->kosts()
            ->with(['facilities', 'images'])
            ->latest()
            ->paginate(12);

        return Inertia::render('PemilikKost/KostList', [
            'kosts' => $kosts,
        ]);
    }

    /**
     * Form tambah kost baru
     */
    public function create()
    {
        $facilityCategories = KostFacility::getCategories();

        return Inertia::render('PemilikKost/KostForm', [
            'facilityCategories' => $facilityCategories,
        ]);
    }

    /**
     * Simpan kost baru
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'required|string|max:2000',
            'address' => 'required|string|max:500',
            'city' => 'required|string|max:100',
            'province' => 'required|string|max:100',
            'postal_code' => 'nullable|string|max:10',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'price_monthly' => 'required|numeric|min:0',
            'price_daily' => 'nullable|numeric|min:0',
            'room_count' => 'required|integer|min:1',
            'available_rooms' => 'required|integer|min:0',
            'gender_type' => 'required|in:putra,putri,campur',
            'kost_type' => 'required|in:bulanan,harian,keduanya',
            'facilities' => 'nullable|array',
            'facilities.*.name' => 'required|string|max:255',
            'facilities.*.description' => 'nullable|string|max:500',
            'facilities.*.category' => 'required|in:kamar,kamar_mandi,umum,keamanan,parkir',
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:2048',
        ], [
            'name.required' => 'Nama kost harus diisi.',
            'description.required' => 'Deskripsi kost harus diisi.',
            'address.required' => 'Alamat kost harus diisi.',
            'city.required' => 'Kota harus diisi.',
            'province.required' => 'Provinsi harus diisi.',
            'price_monthly.required' => 'Harga bulanan harus diisi.',
            'room_count.required' => 'Jumlah kamar harus diisi.',
            'available_rooms.required' => 'Jumlah kamar tersedia harus diisi.',
            'gender_type.required' => 'Tipe gender harus dipilih.',
            'kost_type.required' => 'Tipe kost harus dipilih.',
            'images.*.image' => 'File harus berupa gambar.',
            'images.*.mimes' => 'Format gambar harus jpeg, png, jpg, atau webp.',
            'images.*.max' => 'Ukuran gambar maksimal 2MB.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Validasi available_rooms tidak boleh lebih dari room_count
        if ($request->available_rooms > $request->room_count) {
            return redirect()->back()
                ->withErrors(['available_rooms' => 'Jumlah kamar tersedia tidak boleh lebih dari total kamar.'])
                ->withInput();
        }

        // Buat kost baru
        $kost = Kost::create([
            'owner_id' => Auth::id(),
            'name' => $request->name,
            'description' => $request->description,
            'address' => $request->address,
            'city' => $request->city,
            'province' => $request->province,
            'postal_code' => $request->postal_code,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'price_monthly' => $request->price_monthly,
            'price_daily' => $request->price_daily,
            'room_count' => $request->room_count,
            'available_rooms' => $request->available_rooms,
            'gender_type' => $request->gender_type,
            'kost_type' => $request->kost_type,
            'status' => 'draft',
        ]);

        // Simpan fasilitas
        if ($request->has('facilities')) {
            foreach ($request->facilities as $facility) {
                KostFacility::create([
                    'kost_id' => $kost->id,
                    'name' => $facility['name'],
                    'description' => $facility['description'] ?? null,
                    'category' => $facility['category'],
                ]);
            }
        }

        // Upload dan simpan gambar
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $path = $image->store('kost-images', 'public');

                KostImage::create([
                    'kost_id' => $kost->id,
                    'image_path' => $path,
                    'image_type' => $index === 0 ? 'cover' : 'room',
                    'alt_text' => $kost->name . ' - Gambar ' . ($index + 1),
                    'sort_order' => $index,
                ]);
            }
        }

        return redirect()->route('pemilik.kosts.show', $kost)
            ->with('success', 'Kost berhasil ditambahkan. Status: Draft - Silakan submit untuk review admin.');
    }
