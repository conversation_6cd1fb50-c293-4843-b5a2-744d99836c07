import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Kost } from '@/types';
import { useForm } from '@inertiajs/react';
import { Mail, MessageCircle, Phone, Send, User } from 'lucide-react';
import React from 'react';

interface InquiryFormProps {
    kost: Kost;
    onSuccess?: () => void;
    onCancel?: () => void;
    className?: string;
}

interface InquiryFormData {
    message: string;
    contact_preference: 'email' | 'phone' | 'whatsapp';
}

const InquiryForm: React.FC<InquiryFormProps> = ({
    kost,
    onSuccess,
    onCancel,
    className = '',
}) => {
    const { data, setD<PERSON>, post, processing, errors, reset } = useForm<InquiryFormData>({
        message: '',
        contact_preference: 'email',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        post(route('inquiries.store', kost.id), {
            onSuccess: () => {
                reset();
                onSuccess?.();
            },
        });
    };

    const getContactIcon = (preference: string) => {
        switch (preference) {
            case 'email': return <Mail className="h-4 w-4" />;
            case 'phone': return <Phone className="h-4 w-4" />;
            case 'whatsapp': return <MessageCircle className="h-4 w-4" />;
            default: return <Mail className="h-4 w-4" />;
        }
    };

    const getContactLabel = (preference: string) => {
        switch (preference) {
            case 'email': return 'Email';
            case 'phone': return 'Telepon';
            case 'whatsapp': return 'WhatsApp';
            default: return 'Email';
        }
    };

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5" />
                    Kirim Pertanyaan
                </CardTitle>
                <div className="text-sm text-muted-foreground">
                    <p className="font-medium">{kost.name}</p>
                    <p>{kost.city}, {kost.province}</p>
                </div>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* Kost Info Summary */}
                    <div className="p-3 bg-secondary/50 rounded-lg">
                        <div className="flex items-center gap-2 text-sm">
                            <User className="h-4 w-4 text-muted-foreground" />
                            <span className="font-medium">Pemilik:</span>
                            <span>{kost.owner?.name || 'Tidak diketahui'}</span>
                        </div>
                        <div className="mt-2 text-sm text-muted-foreground">
                            <p>Harga: <span className="font-medium text-foreground">{kost.formatted_price}/bulan</span></p>
                            <p>Kamar tersedia: <span className="font-medium text-foreground">{kost.available_rooms} kamar</span></p>
                        </div>
                    </div>

                    {/* Message Field */}
                    <div className="space-y-2">
                        <Label htmlFor="message">
                            Pesan <span className="text-red-500">*</span>
                        </Label>
                        <Textarea
                            id="message"
                            placeholder="Tulis pertanyaan atau pesan Anda di sini...&#10;&#10;Contoh:&#10;- Apakah masih ada kamar kosong?&#10;- Boleh lihat kamar dulu?&#10;- Fasilitas apa saja yang tersedia?&#10;- Bagaimana sistem pembayaran?"
                            value={data.message}
                            onChange={(e) => setData('message', e.target.value)}
                            className="min-h-[120px] resize-none"
                            disabled={processing}
                        />
                        {errors.message && (
                            <p className="text-sm text-red-600">{errors.message}</p>
                        )}
                    </div>

                    {/* Contact Preference */}
                    <div className="space-y-2">
                        <Label htmlFor="contact_preference">
                            Cara Kontak yang Diinginkan
                        </Label>
                        <Select
                            value={data.contact_preference}
                            onValueChange={(value: 'email' | 'phone' | 'whatsapp') => 
                                setData('contact_preference', value)
                            }
                            disabled={processing}
                        >
                            <SelectTrigger>
                                <SelectValue>
                                    <div className="flex items-center gap-2">
                                        {getContactIcon(data.contact_preference)}
                                        {getContactLabel(data.contact_preference)}
                                    </div>
                                </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="email">
                                    <div className="flex items-center gap-2">
                                        <Mail className="h-4 w-4" />
                                        Email
                                    </div>
                                </SelectItem>
                                <SelectItem value="phone">
                                    <div className="flex items-center gap-2">
                                        <Phone className="h-4 w-4" />
                                        Telepon
                                    </div>
                                </SelectItem>
                                <SelectItem value="whatsapp">
                                    <div className="flex items-center gap-2">
                                        <MessageCircle className="h-4 w-4" />
                                        WhatsApp
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                        {errors.contact_preference && (
                            <p className="text-sm text-red-600">{errors.contact_preference}</p>
                        )}
                    </div>

                    {/* Info Text */}
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <p className="text-sm text-blue-800">
                            <strong>Catatan:</strong> Pesan Anda akan dikirim langsung ke pemilik kost. 
                            Mereka akan menghubungi Anda melalui cara kontak yang dipilih.
                        </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-3 pt-2">
                        {onCancel && (
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onCancel}
                                disabled={processing}
                                className="flex-1"
                            >
                                Batal
                            </Button>
                        )}
                        <Button
                            type="submit"
                            disabled={processing || !data.message.trim()}
                            className="flex-1"
                        >
                            {processing ? (
                                <>
                                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                    Mengirim...
                                </>
                            ) : (
                                <>
                                    <Send className="mr-2 h-4 w-4" />
                                    Kirim Pesan
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
};

export default InquiryForm;
