import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import KostCard from '@/components/kost/kost-card';
import SearchInterface from '@/components/kost/search-interface';
import AppLayout from '@/layouts/app-layout';
import { AiSearchResult, Kost, PageProps } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { 
    Search, 
    Filter, 
    MapPin, 
    DollarSign, 
    Users, 
    Star,
    X,
    Sparkles,
    SlidersHorizontal
} from 'lucide-react';
import React, { useState, useEffect } from 'react';

interface SearchProps extends PageProps {
    kosts: {
        data: Kost[];
        links: any[];
        meta: any;
    };
    filters: {
        city?: string;
        gender_type?: string;
        min_price?: number;
        max_price?: number;
        facilities?: string[];
    };
    filterData: {
        cities: string[];
        facilities: string[];
        price_ranges: Array<{
            label: string;
            min: number;
            max: number | null;
        }>;
    };
    aiServiceAvailable: boolean;
}

export default function Search({
    auth,
    kosts,
    filters,
    filterData,
    aiServiceAvailable,
}: SearchProps) {
    const [showFilters, setShowFilters] = useState(false);
    const [searchResults, setSearchResults] = useState<Kost[]>([]);
    const [isSearching, setIsSearching] = useState(false);
    const [activeFilters, setActiveFilters] = useState(filters);

    const handleAiSearch = async (query: string): Promise<AiSearchResult> => {
        setIsSearching(true);
        try {
            const response = await fetch('/api/ai-search/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ query }),
            });

            const result = await response.json();
            
            if (result.success && result.data?.results) {
                setSearchResults(result.data.results);
            }
            
            return result;
        } catch (error) {
            console.error('AI Search error:', error);
            return {
                success: false,
                message: 'Terjadi kesalahan saat mencari. Silakan coba lagi.',
            };
        } finally {
            setIsSearching(false);
        }
    };

    const handleFilterChange = (key: string, value: any) => {
        const newFilters = { ...activeFilters, [key]: value };
        setActiveFilters(newFilters);
        
        // Apply filters
        router.get(route('pencari.search'), newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearFilter = (key: string) => {
        const newFilters = { ...activeFilters };
        delete newFilters[key];
        setActiveFilters(newFilters);
        
        router.get(route('pencari.search'), newFilters, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const clearAllFilters = () => {
        setActiveFilters({});
        router.get(route('pencari.search'), {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleViewKost = (kost: Kost) => {
        router.visit(route('pencari.kost.show', kost.id));
    };

    const handleInquiryKost = (kost: Kost) => {
        router.visit(route('pencari.kost.show', kost.id), {
            data: { showInquiry: true },
        });
    };

    const activeFilterCount = Object.keys(activeFilters).length;
    const displayKosts = searchResults.length > 0 ? searchResults : kosts.data;

    return (
        <AppLayout>
            <Head title="Cari Kost" />

            <div className="space-y-8">
                {/* Page Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Cari Kost</h1>
                        <p className="text-muted-foreground">
                            Temukan kost yang sesuai dengan kebutuhan Anda
                        </p>
                    </div>
                    <Button
                        variant="outline"
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center gap-2"
                    >
                        <SlidersHorizontal className="h-4 w-4" />
                        Filter
                        {activeFilterCount > 0 && (
                            <Badge variant="secondary" className="ml-1">
                                {activeFilterCount}
                            </Badge>
                        )}
                    </Button>
                </div>

                {/* AI Search Section */}
                {aiServiceAvailable && (
                    <SearchInterface
                        onSearch={handleAiSearch}
                        isLoading={isSearching}
                    />
                )}

                {/* Filters */}
                {showFilters && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filter Pencarian
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                {/* City Filter */}
                                <div className="space-y-2">
                                    <Label>Kota</Label>
                                    <Select
                                        value={activeFilters.city || ''}
                                        onValueChange={(value) => handleFilterChange('city', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih kota">
                                                {activeFilters.city && (
                                                    <div className="flex items-center gap-2">
                                                        <MapPin className="h-4 w-4" />
                                                        {activeFilters.city}
                                                    </div>
                                                )}
                                            </SelectValue>
                                        </SelectTrigger>
                                        <SelectContent>
                                            {filterData.cities.map((city) => (
                                                <SelectItem key={city} value={city}>
                                                    <div className="flex items-center gap-2">
                                                        <MapPin className="h-4 w-4" />
                                                        {city}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Gender Type Filter */}
                                <div className="space-y-2">
                                    <Label>Tipe Gender</Label>
                                    <Select
                                        value={activeFilters.gender_type || ''}
                                        onValueChange={(value) => handleFilterChange('gender_type', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Pilih tipe">
                                                {activeFilters.gender_type && (
                                                    <div className="flex items-center gap-2">
                                                        <Users className="h-4 w-4" />
                                                        {activeFilters.gender_type === 'putra' ? 'Putra' :
                                                         activeFilters.gender_type === 'putri' ? 'Putri' : 'Campur'}
                                                    </div>
                                                )}
                                            </SelectValue>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="putra">
                                                <div className="flex items-center gap-2">
                                                    <Users className="h-4 w-4" />
                                                    Putra
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="putri">
                                                <div className="flex items-center gap-2">
                                                    <Users className="h-4 w-4" />
                                                    Putri
                                                </div>
                                            </SelectItem>
                                            <SelectItem value="campur">
                                                <div className="flex items-center gap-2">
                                                    <Users className="h-4 w-4" />
                                                    Campur
                                                </div>
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Price Range */}
                                <div className="space-y-2">
                                    <Label>Harga Minimum</Label>
                                    <Input
                                        type="number"
                                        placeholder="Rp 0"
                                        value={activeFilters.min_price || ''}
                                        onChange={(e) => handleFilterChange('min_price', e.target.value ? parseInt(e.target.value) : undefined)}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label>Harga Maksimum</Label>
                                    <Input
                                        type="number"
                                        placeholder="Tidak terbatas"
                                        value={activeFilters.max_price || ''}
                                        onChange={(e) => handleFilterChange('max_price', e.target.value ? parseInt(e.target.value) : undefined)}
                                    />
                                </div>
                            </div>

                            {/* Quick Price Ranges */}
                            <div className="space-y-2">
                                <Label>Range Harga Cepat</Label>
                                <div className="flex flex-wrap gap-2">
                                    {filterData.price_ranges.map((range, index) => (
                                        <Button
                                            key={index}
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                handleFilterChange('min_price', range.min);
                                                handleFilterChange('max_price', range.max);
                                            }}
                                            className="flex items-center gap-2"
                                        >
                                            <DollarSign className="h-3 w-3" />
                                            {range.label}
                                        </Button>
                                    ))}
                                </div>
                            </div>

                            {/* Clear Filters */}
                            {activeFilterCount > 0 && (
                                <div className="flex justify-end">
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={clearAllFilters}
                                        className="text-muted-foreground"
                                    >
                                        <X className="h-4 w-4 mr-2" />
                                        Hapus Semua Filter
                                    </Button>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                )}

                {/* Active Filters */}
                {activeFilterCount > 0 && (
                    <div className="flex flex-wrap gap-2">
                        {activeFilters.city && (
                            <Badge variant="secondary" className="flex items-center gap-2">
                                <MapPin className="h-3 w-3" />
                                {activeFilters.city}
                                <button
                                    onClick={() => clearFilter('city')}
                                    className="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
                                >
                                    <X className="h-3 w-3" />
                                </button>
                            </Badge>
                        )}
                        {activeFilters.gender_type && (
                            <Badge variant="secondary" className="flex items-center gap-2">
                                <Users className="h-3 w-3" />
                                {activeFilters.gender_type === 'putra' ? 'Putra' :
                                 activeFilters.gender_type === 'putri' ? 'Putri' : 'Campur'}
                                <button
                                    onClick={() => clearFilter('gender_type')}
                                    className="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
                                >
                                    <X className="h-3 w-3" />
                                </button>
                            </Badge>
                        )}
                        {(activeFilters.min_price || activeFilters.max_price) && (
                            <Badge variant="secondary" className="flex items-center gap-2">
                                <DollarSign className="h-3 w-3" />
                                {activeFilters.min_price ? `Rp ${activeFilters.min_price.toLocaleString()}` : 'Rp 0'} - 
                                {activeFilters.max_price ? ` Rp ${activeFilters.max_price.toLocaleString()}` : ' ∞'}
                                <button
                                    onClick={() => {
                                        clearFilter('min_price');
                                        clearFilter('max_price');
                                    }}
                                    className="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
                                >
                                    <X className="h-3 w-3" />
                                </button>
                            </Badge>
                        )}
                    </div>
                )}

                {/* Results */}
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <h2 className="text-xl font-semibold">
                                {searchResults.length > 0 ? (
                                    <span className="flex items-center gap-2">
                                        <Sparkles className="h-5 w-5 text-primary" />
                                        Hasil Pencarian AI
                                    </span>
                                ) : (
                                    'Hasil Pencarian'
                                )}
                            </h2>
                            <Badge variant="secondary">
                                {displayKosts.length} kost ditemukan
                            </Badge>
                        </div>
                    </div>

                    {displayKosts.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {displayKosts.map((kost) => (
                                <KostCard
                                    key={kost.id}
                                    kost={kost}
                                    onView={handleViewKost}
                                    onInquiry={handleInquiryKost}
                                />
                            ))}
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="flex items-center justify-center py-12">
                                <div className="text-center text-muted-foreground">
                                    <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <h3 className="text-lg font-medium mb-2">Tidak ada kost ditemukan</h3>
                                    <p>Coba ubah filter pencarian atau gunakan kata kunci yang berbeda</p>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Pagination */}
                    {searchResults.length === 0 && kosts.links && kosts.links.length > 3 && (
                        <div className="flex justify-center">
                            <div className="flex gap-2">
                                {kosts.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? "default" : "outline"}
                                        size="sm"
                                        disabled={!link.url}
                                        onClick={() => link.url && router.visit(link.url)}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </AppLayout>
    );
}
