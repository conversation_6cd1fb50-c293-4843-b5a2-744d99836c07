<?php

namespace App\Http\Controllers;

use App\Models\Kost;
use App\Models\Inquiry;
use App\Models\Notification;
use App\Services\GroqAiService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class PencariKostController extends Controller
{
    private GroqAiService $groqAiService;

    public function __construct(GroqAiService $groqAiService)
    {
        $this->middleware(['auth', 'role:pencari_kost']);
        $this->groqAiService = $groqAiService;
    }

    /**
     * Dashboard utama pencari kost
     */
    public function dashboard()
    {
        $user = Auth::user();

        // Statistik untuk dashboard
        $stats = [
            'total_inquiries' => $user->inquiries()->count(),
            'pending_inquiries' => $user->inquiries()->pending()->count(),
            'responded_inquiries' => $user->inquiries()->responded()->count(),
            'unread_notifications' => $user->notifications()->unread()->count(),
        ];

        // Kost terbaru yang disetujui
        $latestKosts = Kost::with(['facilities', 'images', 'owner'])
            ->approved()
            ->active()
            ->latest()
            ->limit(6)
            ->get();

        // Inquiry terbaru user
        $recentInquiries = $user->inquiries()
            ->with(['kost'])
            ->latest()
            ->limit(5)
            ->get();

        // Notifikasi terbaru
        $recentNotifications = $user->notifications()
            ->latest()
            ->limit(5)
            ->get();

        return Inertia::render('PencariKost/Dashboard', [
            'stats' => $stats,
            'latestKosts' => $latestKosts,
            'recentInquiries' => $recentInquiries,
            'recentNotifications' => $recentNotifications,
            'aiServiceAvailable' => $this->groqAiService->isAvailable(),
        ]);
    }

    /**
     * Halaman pencarian kost
     */
    public function search(Request $request)
    {
        $query = Kost::with(['facilities', 'images', 'owner'])
            ->approved()
            ->active();

        // Filter berdasarkan parameter
        if ($request->filled('city')) {
            $query->byCity($request->city);
        }

        if ($request->filled('gender_type')) {
            $query->byGender($request->gender_type);
        }

        if ($request->filled('min_price') || $request->filled('max_price')) {
            $query->byPriceRange($request->min_price, $request->max_price);
        }

        if ($request->filled('facilities')) {
            $facilities = is_array($request->facilities) ? $request->facilities : [$request->facilities];
            $query->whereHas('facilities', function ($q) use ($facilities) {
                $q->whereIn('name', $facilities);
            });
        }

        $kosts = $query->paginate(12)->withQueryString();

        // Data untuk filter
        $filterData = [
            'cities' => Kost::approved()->active()->distinct()->pluck('city')->sort()->values(),
            'facilities' => \App\Models\KostFacility::distinct()->pluck('name')->sort()->values(),
            'price_ranges' => [
                ['label' => 'Di bawah Rp 1 juta', 'min' => 0, 'max' => 1000000],
                ['label' => 'Rp 1 - 2 juta', 'min' => 1000000, 'max' => 2000000],
                ['label' => 'Rp 2 - 3 juta', 'min' => 2000000, 'max' => 3000000],
                ['label' => 'Di atas Rp 3 juta', 'min' => 3000000, 'max' => null],
            ],
        ];

        return Inertia::render('PencariKost/Search', [
            'kosts' => $kosts,
            'filters' => $request->only(['city', 'gender_type', 'min_price', 'max_price', 'facilities']),
            'filterData' => $filterData,
            'aiServiceAvailable' => $this->groqAiService->isAvailable(),
        ]);
    }

    /**
     * Detail kost
     */
    public function show(Kost $kost)
    {
        // Pastikan kost disetujui dan aktif
        if ($kost->status !== 'approved' || !$kost->is_active) {
            abort(404);
        }

        $kost->load(['facilities', 'images', 'owner']);

        // Kost serupa
        $similarKosts = Kost::with(['facilities', 'images'])
            ->approved()
            ->active()
            ->where('id', '!=', $kost->id)
            ->where(function ($query) use ($kost) {
                $query->where('city', $kost->city)
                      ->orWhere('gender_type', $kost->gender_type)
                      ->orWhereBetween('price_monthly', [
                          $kost->price_monthly * 0.8,
                          $kost->price_monthly * 1.2
                      ]);
            })
            ->limit(4)
            ->get();

        return Inertia::render('PencariKost/KostDetail', [
            'kost' => $kost,
            'similarKosts' => $similarKosts,
        ]);
    }

    /**
     * Riwayat inquiry user
     */
    public function inquiries()
    {
        $user = Auth::user();

        $inquiries = $user->inquiries()
            ->with(['kost.images'])
            ->latest()
            ->paginate(10);

        return Inertia::render('PencariKost/Inquiries', [
            'inquiries' => $inquiries,
        ]);
    }

    /**
     * Notifikasi user
     */
    public function notifications()
    {
        $user = Auth::user();

        $notifications = $user->notifications()
            ->latest()
            ->paginate(20);

        // Mark all as read
        $user->notifications()->unread()->update(['read_at' => now()]);

        return Inertia::render('PencariKost/Notifications', [
            'notifications' => $notifications,
        ]);
    }
}
