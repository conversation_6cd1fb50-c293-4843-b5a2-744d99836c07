<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Inquiry;
use App\Models\Kost;
use App\Models\Notification;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class InquiryController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:sanctum');
    }

    /**
     * Display a listing of inquiries for authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $query = Inquiry::with(['kost.images', 'user']);

        // Filter berdasarkan role user
        if ($user->isPencariKost()) {
            // Pencari kost hanya bisa melihat inquiry mereka sendiri
            $query->where('user_id', $user->id);
        } elseif ($user->isPemilikKost()) {
            // Pemilik kost hanya bisa melihat inquiry untuk kost mereka
            $query->whereHas('kost', function ($q) use ($user) {
                $q->where('owner_id', $user->id);
            });
        } elseif ($user->isAdmin()) {
            // Admin bisa melihat semua inquiry
            // No additional filter needed
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak.',
            ], 403);
        }

        // Filter berdasarkan status
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        // Filter berdasarkan kost
        if ($request->filled('kost_id')) {
            $query->where('kost_id', $request->kost_id);
        }

        $perPage = min($request->get('per_page', 15), 50);
        $inquiries = $query->latest()->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $inquiries->items(),
            'meta' => [
                'current_page' => $inquiries->currentPage(),
                'last_page' => $inquiries->lastPage(),
                'per_page' => $inquiries->perPage(),
                'total' => $inquiries->total(),
            ],
        ]);
    }

    /**
     * Store a newly created inquiry
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();

        // Pastikan user adalah pencari kost
        if (!$user->isPencariKost()) {
            return response()->json([
                'success' => false,
                'message' => 'Hanya pencari kost yang dapat mengirim inquiry.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'kost_id' => 'required|exists:kosts,id',
            'message' => 'required|string|min:10|max:1000',
            'contact_preference' => 'required|in:email,phone,whatsapp',
        ], [
            'kost_id.required' => 'Kost harus dipilih.',
            'kost_id.exists' => 'Kost tidak ditemukan.',
            'message.required' => 'Pesan harus diisi.',
            'message.min' => 'Pesan minimal 10 karakter.',
            'message.max' => 'Pesan maksimal 1000 karakter.',
            'contact_preference.required' => 'Pilih cara kontak yang diinginkan.',
            'contact_preference.in' => 'Cara kontak tidak valid.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Cek kost
        $kost = Kost::find($request->kost_id);
        if ($kost->status !== 'approved' || !$kost->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Kost tidak tersedia untuk inquiry.',
            ], 400);
        }

        // Cek apakah user sudah pernah mengirim inquiry untuk kost ini dalam 24 jam terakhir
        $existingInquiry = Inquiry::where('kost_id', $kost->id)
            ->where('user_id', $user->id)
            ->where('created_at', '>=', now()->subDay())
            ->first();

        if ($existingInquiry) {
            return response()->json([
                'success' => false,
                'message' => 'Anda sudah mengirim inquiry untuk kost ini dalam 24 jam terakhir.',
            ], 400);
        }

        // Buat inquiry baru
        $inquiry = Inquiry::create([
            'kost_id' => $kost->id,
            'user_id' => $user->id,
            'message' => $request->message,
            'contact_preference' => $request->contact_preference,
        ]);

        // Buat notifikasi untuk pemilik kost
        Notification::createInquiryNotification(
            $kost->owner_id,
            $kost->name,
            $inquiry->id
        );

        $inquiry->load(['kost.images', 'user']);

        return response()->json([
            'success' => true,
            'message' => 'Inquiry berhasil dikirim. Pemilik kost akan menghubungi Anda segera.',
            'data' => $inquiry,
        ], 201);
    }

    /**
     * Display the specified inquiry
     */
    public function show(Inquiry $inquiry): JsonResponse
    {
        $user = Auth::user();

        // Pastikan user memiliki akses ke inquiry ini
        if (!$user->isAdmin() && $inquiry->user_id !== $user->id && $inquiry->kost->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak.',
            ], 403);
        }

        $inquiry->load(['kost.images', 'user']);

        return response()->json([
            'success' => true,
            'data' => $inquiry,
        ]);
    }

    /**
     * Update inquiry status (for kost owner)
     */
    public function updateStatus(Request $request, Inquiry $inquiry): JsonResponse
    {
        $user = Auth::user();

        // Pastikan user adalah pemilik kost atau admin
        if (!$user->isAdmin() && $inquiry->kost->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak.',
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:responded,closed',
        ], [
            'status.required' => 'Status harus dipilih.',
            'status.in' => 'Status tidak valid.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validasi gagal.',
                'errors' => $validator->errors(),
            ], 422);
        }

        if ($request->status === 'responded') {
            $inquiry->markAsResponded();
            $message = 'Inquiry ditandai sebagai sudah direspon.';
        } else {
            $inquiry->close();
            $message = 'Inquiry ditutup.';
        }

        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $inquiry->fresh(),
        ]);
    }

    /**
     * Remove the specified inquiry
     */
    public function destroy(Inquiry $inquiry): JsonResponse
    {
        $user = Auth::user();

        // Pastikan user adalah pemilik inquiry atau admin
        if (!$user->isAdmin() && $inquiry->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak.',
            ], 403);
        }

        $inquiry->delete();

        return response()->json([
            'success' => true,
            'message' => 'Inquiry berhasil dihapus.',
        ]);
    }

    /**
     * Get inquiry statistics for authenticated user
     */
    public function stats(): JsonResponse
    {
        $user = Auth::user();

        if ($user->isPencariKost()) {
            $stats = [
                'total_inquiries' => $user->inquiries()->count(),
                'pending_inquiries' => $user->inquiries()->pending()->count(),
                'responded_inquiries' => $user->inquiries()->responded()->count(),
                'closed_inquiries' => $user->inquiries()->where('status', 'closed')->count(),
            ];
        } elseif ($user->isPemilikKost()) {
            $stats = [
                'total_inquiries' => Inquiry::whereHas('kost', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                })->count(),
                'pending_inquiries' => Inquiry::whereHas('kost', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                })->pending()->count(),
                'responded_inquiries' => Inquiry::whereHas('kost', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                })->responded()->count(),
                'closed_inquiries' => Inquiry::whereHas('kost', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                })->where('status', 'closed')->count(),
            ];
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Akses ditolak.',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}
